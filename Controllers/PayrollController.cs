using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using PayrollVoting.Models;

namespace PayrollVoting.Controllers
{
    public class PayrollController : Controller
    {
        // GET: Payroll/Salary
        public ActionResult Salary()
        {
            return View(new Employee());
        }

        // POST: Payroll/Salary
        [HttpPost]
        public ActionResult Salary(Employee employee)
        {
            if (ModelState.IsValid)
            {
                // The calculations are handled by the model properties
                ViewBag.ShowResults = true;
                return View(employee);
            }

            return View(employee);
        }
    }
}
