using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using PayrollVoting.Models;

namespace PayrollVoting.Controllers
{
    public class VotingController : Controller
    {
        // GET: Voting/Age
        public ActionResult Age()
        {
            return View(new Voter());
        }

        // POST: Voting/Age
        [HttpPost]
        public ActionResult Age(Voter voter)
        {
            if (ModelState.IsValid)
            {
                ViewBag.ShowResults = true;
                return View(voter);
            }

            return View(voter);
        }
    }
}
