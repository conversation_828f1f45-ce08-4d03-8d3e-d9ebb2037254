@model PayrollVoting.Models.Employee

@{
    ViewBag.Title = "Payroll System - Salary Calculator";
}

<h2>Payroll System - Employee Salary Calculator</h2>

@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()
    
    <div class="form-horizontal">
        <h4>Enter Employee Information</h4>
        <hr />
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })

        <div class="form-group">
            @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Name, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Name, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.EmployeeId, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.EmployeeId, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.EmployeeId, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Position, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Position, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Position, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Department, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Department, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Department, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.RatePerDay, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.RatePerDay, new { htmlAttributes = new { @class = "form-control", @type = "number", @step = "0.01" } })
                @Html.ValidationMessageFor(model => model.RatePerDay, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.DaysWorked, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.DaysWorked, new { htmlAttributes = new { @class = "form-control", @type = "number" } })
                @Html.ValidationMessageFor(model => model.DaysWorked, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <input type="submit" value="Calculate Salary" class="btn btn-primary" />
            </div>
        </div>
    </div>
}

@if (ViewBag.ShowResults == true)
{
    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">Employee Details and Salary Computation</h3>
        </div>
        <div class="panel-body">
            <h4>Employee Information:</h4>
            <table class="table table-bordered">
                <tr>
                    <td><strong>Name:</strong></td>
                    <td>@Model.Name</td>
                </tr>
                <tr>
                    <td><strong>Employee ID:</strong></td>
                    <td>@Model.EmployeeId</td>
                </tr>
                <tr>
                    <td><strong>Position:</strong></td>
                    <td>@Model.Position</td>
                </tr>
                <tr>
                    <td><strong>Department:</strong></td>
                    <td>@Model.Department</td>
                </tr>
                <tr>
                    <td><strong>Rate Per Day:</strong></td>
                    <td>₱@Model.RatePerDay.ToString("N2")</td>
                </tr>
                <tr>
                    <td><strong>Days Worked:</strong></td>
                    <td>@Model.DaysWorked</td>
                </tr>
            </table>

            <h4>Salary Computation:</h4>
            <table class="table table-bordered">
                <tr>
                    <td><strong>Gross Pay:</strong></td>
                    <td>₱@Model.GrossPay.ToString("N2")</td>
                </tr>
                <tr class="info">
                    <td colspan="2"><strong>Deductions:</strong></td>
                </tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;SSS (5%):</td>
                    <td>₱@Model.SSSDeduction.ToString("N2")</td>
                </tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;Philhealth (5%):</td>
                    <td>₱@Model.PhilhealthDeduction.ToString("N2")</td>
                </tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;PAG-IBIG (5%):</td>
                    <td>₱@Model.PagIbigDeduction.ToString("N2")</td>
                </tr>
                <tr>
                    <td>&nbsp;&nbsp;&nbsp;&nbsp;Tax (5%):</td>
                    <td>₱@Model.TaxDeduction.ToString("N2")</td>
                </tr>
                <tr class="warning">
                    <td><strong>Total Deductions:</strong></td>
                    <td><strong>₱@Model.TotalDeductions.ToString("N2")</strong></td>
                </tr>
                <tr class="success">
                    <td><strong>Net Pay:</strong></td>
                    <td><strong>₱@Model.NetPay.ToString("N2")</strong></td>
                </tr>
            </table>
        </div>
    </div>
}

<div>
    @Html.ActionLink("Back to Home", "Index", "Home")
</div>
