﻿@{
    var uname = Request["uname"].AsInt();
    var pword = Request["pword"].AsInt();

   IEnumerable<String> strCourses = new List<String> { "BSCS", "BSIT", "BSBA", "BSIE", "BSECE" };
}

<form>
    <h2>Login Form</h2>
    <label>Username:</label>
    @Html.TextBox("uname") <br /><br />
    <label>Password:</label>
    @Html.Password("pword") <br /><br />
    Male <input type="radio" name="gender" value="Male" />
    Female @Html.RadioButton("gender", "Female", false)<br /><br />
    <label>Address:</label>
    @Html.TextArea("address") <br /><br />
    <label>Hobbies:</label>
    Dancing @Html.CheckBox("Dancing") Basketball @Html.CheckBox("Basketball")<br /><br />
    <label>Course:</label>
    @Html.DropDownList("ddlCourses", new SelectList(strCourses), "Select Course")

    <input type="submit" value="Login!" /><br />
</form>

<hr />

<div class="row">
    <div class="col-md-6">
        <h3>Payroll System</h3>
        <p>Calculate employee salary with automatic deductions for SSS, Philhealth, PAG-IBIG, and Tax.</p>
        <p><a class="btn btn-primary" href="@Url.Action("Salary", "Payroll")">Go to Payroll System &raquo;</a></p>
    </div>
    <div class="col-md-6">
        <h3>Voting System</h3>
        <p>Verify voter registration and age eligibility for voting.</p>
        <p><a class="btn btn-success" href="@Url.Action("Age", "Voting")">Go to Voting System &raquo;</a></p>
    </div>
</div>