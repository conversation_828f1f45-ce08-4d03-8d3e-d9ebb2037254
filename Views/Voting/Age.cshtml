@model PayrollVoting.Models.Voter

@{
    ViewBag.Title = "Voting System - Voter Eligibility Check";
}

<h2>Voting System - Voter Registration Verification</h2>

@using (Html.BeginForm())
{
    @Html.AntiForgeryToken()
    
    <div class="form-horizontal">
        <h4>Enter Voter Information</h4>
        <hr />
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })

        <div class="form-group">
            @Html.LabelFor(model => model.Name, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Name, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Name, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Age, htmlAttributes: new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Age, new { htmlAttributes = new { @class = "form-control", @type = "number", @min = "1", @max = "120" } })
                @Html.ValidationMessageFor(model => model.Age, "", new { @class = "text-danger" })
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <div class="checkbox">
                    @Html.CheckBoxFor(model => model.IsRegistered)
                    @Html.LabelFor(model => model.IsRegistered, "Registered to Vote")
                </div>
            </div>
        </div>

        <div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <input type="submit" value="Check Eligibility" class="btn btn-primary" />
            </div>
        </div>
    </div>
}

@if (ViewBag.ShowResults == true)
{
    <div class="panel @(Model.EligibilityStatus == "Eligible to Vote" ? "panel-success" : "panel-danger")">
        <div class="panel-heading">
            <h3 class="panel-title">Voter Eligibility Results</h3>
        </div>
        <div class="panel-body">
            <h4>Voter Information:</h4>
            <table class="table table-bordered">
                <tr>
                    <td><strong>Name:</strong></td>
                    <td>@Model.Name</td>
                </tr>
                <tr>
                    <td><strong>Age:</strong></td>
                    <td>@Model.Age years old</td>
                </tr>
                <tr>
                    <td><strong>Legal Age (18+):</strong></td>
                    <td>@(Model.IsLegalAge ? "Yes" : "No")</td>
                </tr>
                <tr>
                    <td><strong>Registered:</strong></td>
                    <td>@(Model.IsRegistered ? "Yes" : "No")</td>
                </tr>
            </table>

            <div class="alert @(Model.EligibilityStatus == "Eligible to Vote" ? "alert-success" : "alert-danger")" role="alert">
                <h4><strong>Status: @Model.EligibilityStatus</strong></h4>
                @if (Model.EligibilityStatus == "Eligible to Vote")
                {
                    <p>Congratulations! You are eligible to vote in the upcoming elections.</p>
                }
                else if (Model.EligibilityStatus == "Please register first")
                {
                    <p>You meet the age requirement but need to register first before you can vote.</p>
                }
                else
                {
                    <p>You do not meet the requirements to vote at this time.</p>
                }
            </div>
        </div>
    </div>
}

<div>
    @Html.ActionLink("Back to Home", "Index", "Home")
</div>
