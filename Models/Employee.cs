using System;
using System.ComponentModel.DataAnnotations;

namespace PayrollVoting.Models
{
    public class Employee
    {
        [Required]
        [Display(Name = "Employee Name")]
        public string Name { get; set; }

        [Required]
        [Display(Name = "Employee ID")]
        public string EmployeeId { get; set; }

        [Required]
        [Display(Name = "Position")]
        public string Position { get; set; }

        [Required]
        [Display(Name = "Department")]
        public string Department { get; set; }

        [Required]
        [Range(1, double.MaxValue, ErrorMessage = "Rate per day must be greater than 0")]
        [Display(Name = "Rate Per Day")]
        public decimal RatePerDay { get; set; }

        [Required]
        [Range(1, 31, ErrorMessage = "Number of days must be between 1 and 31")]
        [Display(Name = "Number of Days Worked")]
        public int DaysWorked { get; set; }

        // Calculated properties
        public decimal GrossPay => RatePerDay * DaysWorked;

        public decimal SSSDeduction => GrossPay * 0.05m;

        public decimal PhilhealthDeduction => GrossPay * 0.05m;

        public decimal PagIbigDeduction => GrossPay * 0.05m;

        public decimal TaxDeduction => GrossPay * 0.05m;

        public decimal TotalDeductions => SSSDeduction + PhilhealthDeduction + PagIbigDeduction + TaxDeduction;

        public decimal NetPay => GrossPay - TotalDeductions;
    }
}
