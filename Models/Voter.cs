using System.ComponentModel.DataAnnotations;

namespace PayrollVoting.Models
{
    public class Voter
    {
        [Required]
        [Display(Name = "Full Name")]
        public string Name { get; set; }

        [Required]
        [Range(1, 120, ErrorMessage = "Please enter a valid age")]
        [Display(Name = "Age")]
        public int Age { get; set; }

        [Display(Name = "Registered to Vote")]
        public bool IsRegistered { get; set; }

        // Calculated property for eligibility
        public string EligibilityStatus
        {
            get
            {
                if (Age >= 18 && IsRegistered)
                    return "Eligible to Vote";
                else if (Age >= 18 && !IsRegistered)
                    return "Please register first";
                else if (IsRegistered && Age < 18)
                    return "Invalid Input";
                else
                    return "Invalid Input";
            }
        }

        public bool IsLegalAge => Age >= 18;
    }
}
